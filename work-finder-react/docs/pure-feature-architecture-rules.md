# Pure Feature-Based Architecture Rules

## ZERO TOLERANCE POLICY

This document establishes **NON-NEGOTIABLE** rules for pure feature-based architecture. **NO EXCEPTIONS** are allowed. Any deviation from these rules is considered architectural debt and must be immediately corrected.

---

## FORBIDDEN PATTERNS ❌

### 1. Traditional Page-Based Organization
```
❌ FORBIDDEN:
src/pages/
├── auth/
├── jobs/
├── companies/
└── dashboard/

❌ FORBIDDEN:
- Any directory named "pages"
- Any component that acts as a "page wrapper"
- Any routing that references "pages"
- Any concept of "moving pages into features"
```

### 2. Traditional Component Libraries
```
❌ FORBIDDEN:
src/components/
├── common/
├── shared/
├── ui/
└── layout/

❌ FORBIDDEN:
- Generic "components" directories
- "Common" or "shared" component collections
- UI component libraries outside of features
- Layout components not owned by features
```

### 3. Traditional Service Layers
```
❌ FORBIDDEN:
src/services/
├── api/
├── auth/
├── jobs/
└── companies/

❌ FORBIDDEN:
- Centralized service directories
- API layers not owned by features
- Business logic outside of features
- Data access patterns outside of features
```

### 4. Traditional State Management
```
❌ FORBIDDEN:
src/store/
├── slices/
├── reducers/
└── actions/

❌ FORBIDDEN:
- Centralized state management
- Global stores not owned by features
- State that crosses feature boundaries
- Redux-style centralized patterns
```

### 5. Traditional Utility Organization
```
❌ FORBIDDEN:
src/utils/
src/helpers/
src/lib/
src/constants/

❌ FORBIDDEN:
- Generic utility directories
- Helper functions not owned by features
- Constants not owned by features
- Libraries not owned by features
```

---

## MANDATORY PATTERNS ✅

### 1. Pure Feature Domains
```
✅ REQUIRED:
src/features/
├── authentication/
│   ├── routes/           # Feature owns ALL routing
│   ├── components/       # Feature owns ALL components
│   ├── services/         # Feature owns ALL services
│   ├── stores/           # Feature owns ALL state
│   ├── types/            # Feature owns ALL types
│   ├── utils/            # Feature owns ALL utilities
│   ├── constants/        # Feature owns ALL constants
│   ├── hooks/            # Feature owns ALL hooks
│   ├── validators/       # Feature owns ALL validation
│   └── index.ts          # Feature's ONLY public API
```

### 2. Complete Feature Ownership
**EVERY feature MUST own:**
- ✅ All routes and navigation within its domain
- ✅ All components used in its domain
- ✅ All business logic for its domain
- ✅ All data fetching and API calls
- ✅ All state management for its domain
- ✅ All types and interfaces for its domain
- ✅ All utilities specific to its domain
- ✅ All constants used in its domain
- ✅ All validation logic for its domain
- ✅ All error handling for its domain

### 3. Feature-First Shared Resources
```
✅ REQUIRED:
src/shared/
├── design-system/        # Only design tokens and primitives
│   ├── tokens/           # Colors, spacing, typography
│   ├── primitives/       # Basic HTML elements only
│   └── index.ts
├── infrastructure/       # Only technical infrastructure
│   ├── router/           # Feature-agnostic routing setup
│   ├── api/              # HTTP client configuration only
│   └── index.ts
└── types/                # Only cross-feature contracts
    ├── api.ts            # Generic API response types
    ├── common.ts         # Truly generic types only
    └── index.ts
```

### 4. Strict Feature Boundaries
```
✅ REQUIRED:
- Features communicate ONLY through public APIs
- Features NEVER import internal implementations
- Features NEVER share components directly
- Features NEVER share state directly
- Features NEVER share services directly
```

---

## ARCHITECTURAL VALIDATION RULES

### 1. Import Rules
```typescript
✅ ALLOWED:
import { LoginForm } from '@/features/authentication';
import { Button } from '@/shared/design-system';
import { ApiResponse } from '@/shared/types';

❌ FORBIDDEN:
import LoginForm from '@/features/authentication/components/LoginForm';
import { useAuthStore } from '@/features/authentication/stores/authStore';
import JobCard from '@/pages/jobs/components/JobCard';
```

### 2. Directory Rules
```
✅ ALLOWED:
- src/features/{domain}/
- src/shared/design-system/
- src/shared/infrastructure/
- src/shared/types/

❌ FORBIDDEN:
- src/pages/
- src/components/
- src/services/
- src/store/
- src/utils/
- src/lib/
- src/helpers/
- src/constants/
```

### 3. File Naming Rules
```
✅ ALLOWED:
- {FeatureName}Routes.tsx (within feature)
- {FeatureName}Service.ts (within feature)
- {FeatureName}Store.ts (within feature)
- index.ts (public API only)

❌ FORBIDDEN:
- Any file with "Page" suffix
- Any file with "Common" prefix
- Any file with "Shared" prefix
- Any file with "Global" prefix
```

### 4. Component Rules
```
✅ ALLOWED:
- Components that belong to a specific feature domain
- Design system primitives (Button, Input, etc.)
- Infrastructure components (Router, ErrorBoundary)

❌ FORBIDDEN:
- "Page" components
- "Layout" components not owned by features
- "Common" components
- "Shared" business components
```

---

## FEATURE DOMAIN DEFINITIONS

### 1. Authentication Domain
**Owns:** Login, logout, registration, password reset, session management, route protection
**Routes:** `/login`, `/register`, `/forgot-password`, `/reset-password`
**Public API:** Authentication forms, auth hooks, auth guards, user types

### 2. Jobs Domain
**Owns:** Job listings, job search, job details, job bookmarking, job application initiation
**Routes:** `/jobs`, `/jobs/:id`, `/jobs/search`
**Public API:** Job components, job hooks, job services, job types

### 3. Companies Domain
**Owns:** Company listings, company profiles, company following, company reviews
**Routes:** `/companies`, `/companies/:id`
**Public API:** Company components, company hooks, company services, company types

### 4. Applications Domain
**Owns:** Application management, application tracking, interview scheduling
**Routes:** `/applications`, `/applications/:id`
**Public API:** Application components, application hooks, application services

### 5. Profile Domain
**Owns:** User profile, saved jobs, preferences, resume management
**Routes:** `/profile`, `/profile/saved`, `/profile/settings`
**Public API:** Profile components, profile hooks, profile services

### 6. Search Domain
**Owns:** Global search, search suggestions, search history
**Routes:** `/search`
**Public API:** Search components, search hooks, search services

---

## VALIDATION CHECKLIST

### Pre-Development Validation
- [ ] No traditional directories exist (pages/, components/, services/, etc.)
- [ ] All features have complete domain ownership
- [ ] All shared resources follow feature-first principles
- [ ] All imports use public APIs only

### Development Validation
- [ ] New code follows feature domain ownership
- [ ] No traditional patterns are introduced
- [ ] All components belong to a specific feature
- [ ] All services belong to a specific feature

### Post-Development Validation
- [ ] No forbidden import patterns exist
- [ ] No forbidden directory structures exist
- [ ] All features are completely self-contained
- [ ] Architecture purity is maintained

---

## ENFORCEMENT MECHANISMS

### 1. Automated Linting Rules
```javascript
// ESLint rules to enforce architecture
"no-restricted-imports": [
  "error",
  {
    "patterns": [
      "@/pages/*",
      "@/components/*", 
      "@/services/*",
      "@/features/*/components/*",
      "@/features/*/services/*",
      "@/features/*/stores/*"
    ]
  }
]
```

### 2. Directory Structure Validation
```bash
# Script to validate directory structure
if [ -d "src/pages" ]; then
  echo "❌ FORBIDDEN: src/pages directory exists"
  exit 1
fi
```

### 3. Build-Time Validation
- Fail build if forbidden patterns are detected
- Validate all imports use public APIs
- Ensure feature boundaries are maintained

### 4. Code Review Checklist
- [ ] No traditional patterns introduced
- [ ] All code belongs to appropriate feature domain
- [ ] Public APIs are used for cross-feature communication
- [ ] Feature boundaries are respected

---

## MIGRATION STRATEGY

### 1. Complete Elimination Phase
- **DELETE** all traditional directories
- **MOVE** all code into appropriate feature domains
- **VALIDATE** no traditional patterns remain

### 2. Pure Implementation Phase
- **IMPLEMENT** complete feature ownership
- **ESTABLISH** feature public APIs
- **VALIDATE** feature boundaries

### 3. Enforcement Phase
- **IMPLEMENT** automated validation
- **ESTABLISH** development workflows
- **MAINTAIN** architectural purity

**NO GRADUAL MIGRATION. NO HYBRID STATES. COMPLETE TRANSFORMATION ONLY.**
